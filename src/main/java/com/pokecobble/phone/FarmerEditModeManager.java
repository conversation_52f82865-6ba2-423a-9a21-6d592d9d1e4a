package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;

/**
 * Client-side manager for the farmer app edit mode.
 * Edit mode allows operators to modify product details in the farmer app.
 * This is purely client-side - changes are sent to server for persistence.
 */
public class FarmerEditModeManager {
    private static final FarmerEditModeManager INSTANCE = new FarmerEditModeManager();

    // Client-side edit mode state
    private boolean editModeEnabled = false;
    
    private FarmerEditModeManager() {
        // Private constructor for singleton
    }

    /**
     * Gets the singleton instance of the FarmerEditModeManager.
     *
     * @return The FarmerEditModeManager instance
     */
    public static FarmerEditModeManager getInstance() {
        return INSTANCE;
    }

    /**
     * Toggles edit mode for the current client.
     *
     * @return True if edit mode is now enabled, false if it's now disabled
     */
    public boolean toggleEditMode() {
        editModeEnabled = !editModeEnabled;
        Pokecobbleclaim.LOGGER.info("Farmer edit mode " + (editModeEnabled ? "enabled" : "disabled"));
        return editModeEnabled;
    }

    /**
     * Checks if edit mode is enabled for the current client.
     *
     * @return True if edit mode is enabled
     */
    public boolean isEditModeEnabled() {
        return editModeEnabled;
    }

    /**
     * Sets edit mode state for the current client.
     *
     * @param enabled Whether edit mode should be enabled
     */
    public void setEditModeEnabled(boolean enabled) {
        this.editModeEnabled = enabled;
        Pokecobbleclaim.LOGGER.info("Farmer edit mode " + (enabled ? "enabled" : "disabled"));
    }
}
