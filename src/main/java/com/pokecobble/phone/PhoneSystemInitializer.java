package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.client.ClientProductManager;
import com.pokecobble.phone.manager.ServerProductManager;
import com.pokecobble.phone.network.ProductNetworkManager;
import com.pokecobble.phone.network.ProductPurchasePacket;
import com.pokecobble.phone.network.ProductUpdatePacket;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerLifecycleEvents;
import net.minecraft.server.MinecraftServer;

/**
 * Initializes the phone system components including product management,
 * network handlers, and synchronization systems.
 */
public class PhoneSystemInitializer {
    private static boolean serverInitialized = false;
    private static boolean clientInitialized = false;
    
    /**
     * Initializes server-side phone systems.
     */
    public static void initializeServer() {
        if (serverInitialized) {
            return;
        }
        
        try {
            Pokecobbleclaim.LOGGER.info("Initializing server-side phone systems...");
            
            // Initialize server product manager
            ServerProductManager.getInstance();
            
            // Register network handlers
            ProductNetworkManager.registerServerHandlers();
            ProductPurchasePacket.registerServerHandler();
            
            // Register server lifecycle events
            registerServerLifecycleEvents();
            
            serverInitialized = true;
            Pokecobbleclaim.LOGGER.info("Server-side phone systems initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize server-side phone systems", e);
        }
    }
    
    /**
     * Initializes client-side phone systems.
     */
    @Environment(EnvType.CLIENT)
    public static void initializeClient() {
        if (clientInitialized) {
            return;
        }
        
        try {
            Pokecobbleclaim.LOGGER.info("Initializing client-side phone systems...");
            
            // Initialize client product manager
            ClientProductManager clientManager = ClientProductManager.getInstance();
            clientManager.initializeDefaults();
            
            // Register network handlers
            ProductNetworkManager.registerClientHandlers();
            ProductPurchasePacket.registerClientHandler();
            
            // Register client lifecycle events
            registerClientLifecycleEvents();
            
            clientInitialized = true;
            Pokecobbleclaim.LOGGER.info("Client-side phone systems initialized successfully");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize client-side phone systems", e);
        }
    }
    
    /**
     * Registers server lifecycle events.
     */
    private static void registerServerLifecycleEvents() {
        // Server starting event
        ServerLifecycleEvents.SERVER_STARTING.register(server -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Server starting, initializing product data");
            
            // Initialize server product manager if not already done
            ServerProductManager.getInstance();
        });
        
        // Server started event
        ServerLifecycleEvents.SERVER_STARTED.register(server -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Server started, product system ready");
            
            // Optionally sync products to any connected players
            // (though typically no players are connected at server start)
        });
        
        // Server stopping event
        ServerLifecycleEvents.SERVER_STOPPING.register(server -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Server stopping, saving product data");
            
            try {
                ServerProductManager.getInstance().shutdown();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error during phone system shutdown", e);
            }
        });
        
        // Player join event - sync products to new players
        net.fabricmc.fabric.api.networking.v1.ServerPlayConnectionEvents.JOIN.register((handler, sender, server) -> {
            // Delay sync slightly to ensure player is fully loaded
            server.execute(() -> {
                try {
                    Thread.sleep(1000); // 1 second delay
                    
                    java.util.Map<String, com.pokecobble.phone.data.ServerProductData> products = 
                        ServerProductManager.getInstance().getAllProducts();
                    
                    ProductNetworkManager.sendFullProductSync(handler.getPlayer(), products);
                    
                    Pokecobbleclaim.LOGGER.info("Synced {} products to player {}", 
                        products.size(), handler.getPlayer().getName().getString());
                        
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Failed to sync products to player " + 
                        handler.getPlayer().getName().getString(), e);
                }
            });
        });
    }
    
    /**
     * Registers client lifecycle events.
     */
    @Environment(EnvType.CLIENT)
    private static void registerClientLifecycleEvents() {
        // Client connected to server event
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents.JOIN.register((handler, sender, client) -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Connected to server, requesting product sync");
            
            // Request product sync from server
            try {
                // Delay the request slightly to ensure connection is stable
                new Thread(() -> {
                    try {
                        Thread.sleep(2000); // 2 second delay
                        ProductNetworkManager.requestProductSync();
                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.error("Failed to request product sync", e);
                    }
                }, "ProductSyncRequest").start();
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to request initial product sync", e);
            }
        });
        
        // Client disconnected from server event
        net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents.DISCONNECT.register((handler, client) -> {
            Pokecobbleclaim.LOGGER.info("Phone system: Disconnected from server, clearing product cache");
            
            // Clear client product cache
            ClientProductManager.getInstance().clearCache();
        });
    }
    
    /**
     * Performs a system health check.
     */
    public static boolean performHealthCheck() {
        try {
            // Check server systems
            if (serverInitialized) {
                ServerProductManager serverManager = ServerProductManager.getInstance();
                java.util.Map<String, com.pokecobble.phone.data.ServerProductData> products = serverManager.getAllProducts();
                
                if (products.isEmpty()) {
                    Pokecobbleclaim.LOGGER.warn("Phone system health check: No products found on server");
                    return false;
                }
                
                Pokecobbleclaim.LOGGER.info("Phone system health check: Server has {} products", products.size());
            }
            
            // Check client systems (if on client)
            try {
                ClientProductManager clientManager = ClientProductManager.getInstance();
                java.util.Map<String, com.pokecobble.phone.gui.FarmerAppScreen.Product> clientProducts = clientManager.getAllProducts();
                
                Pokecobbleclaim.LOGGER.info("Phone system health check: Client has {} products", clientProducts.size());
            } catch (Exception e) {
                // Client-side check failed, but this might be normal on server
                Pokecobbleclaim.LOGGER.debug("Client-side health check failed (normal on server): {}", e.getMessage());
            }
            
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Phone system health check failed", e);
            return false;
        }
    }
    
    /**
     * Forces a full product synchronization to all connected players.
     */
    public static void forceSyncToAllPlayers(MinecraftServer server) {
        if (!serverInitialized) {
            Pokecobbleclaim.LOGGER.warn("Cannot force sync: Server systems not initialized");
            return;
        }
        
        try {
            ProductNetworkManager.sendFullProductSyncToAll(server);
            Pokecobbleclaim.LOGGER.info("Forced product sync to all connected players");
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to force sync to all players", e);
        }
    }
    
    /**
     * Gets initialization status.
     */
    public static String getInitializationStatus() {
        StringBuilder status = new StringBuilder();
        status.append("Phone System Status:\n");
        status.append("  Server Initialized: ").append(serverInitialized).append("\n");
        status.append("  Client Initialized: ").append(clientInitialized).append("\n");
        
        if (serverInitialized) {
            try {
                int productCount = ServerProductManager.getInstance().getAllProducts().size();
                status.append("  Server Products: ").append(productCount).append("\n");
            } catch (Exception e) {
                status.append("  Server Products: Error - ").append(e.getMessage()).append("\n");
            }
        }
        
        try {
            int clientProductCount = ClientProductManager.getInstance().getAllProducts().size();
            status.append("  Client Products: ").append(clientProductCount).append("\n");
        } catch (Exception e) {
            status.append("  Client Products: Error - ").append(e.getMessage()).append("\n");
        }
        
        return status.toString();
    }
    
    /**
     * Resets the initialization state (for testing purposes).
     */
    public static void resetInitialization() {
        serverInitialized = false;
        clientInitialized = false;
        Pokecobbleclaim.LOGGER.info("Phone system initialization state reset");
    }
}
