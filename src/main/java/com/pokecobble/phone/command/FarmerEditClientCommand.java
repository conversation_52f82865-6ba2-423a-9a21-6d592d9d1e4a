package com.pokecobble.phone.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.phone.FarmerEditModeManager;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.text.Text;

/**
 * Client-side command handler for the /farmer edit command.
 * This command toggles edit mode for the farmer app on the client side.
 */
public class FarmerEditClientCommand {
    
    /**
     * Registers the client-side /farmer edit command.
     * 
     * @param dispatcher The client command dispatcher
     */
    public static void register(CommandDispatcher<FabricClientCommandSource> dispatcher) {
        dispatcher.register(
            ClientCommandManager.literal("farmer")
                .then(ClientCommandManager.literal("edit")
                    .executes(FarmerEditClientCommand::toggleEditMode)
                )
        );
    }
    
    /**
     * Handles the execution of the /farmer edit command on the client.
     * 
     * @param context The command context
     * @return 1 if successful, 0 otherwise
     */
    private static int toggleEditMode(CommandContext<FabricClientCommandSource> context) {
        FabricClientCommandSource source = context.getSource();

        boolean enabled = FarmerEditModeManager.getInstance().toggleEditMode();
        source.sendFeedback(Text.literal("§6Farmer edit mode " + (enabled ? "§aenabled" : "§cdisabled") + "§6."));

        // Debug logging
        com.pokecobble.Pokecobbleclaim.LOGGER.info("Farmer edit mode toggled: {}", enabled);

        return 1;
    }
}
