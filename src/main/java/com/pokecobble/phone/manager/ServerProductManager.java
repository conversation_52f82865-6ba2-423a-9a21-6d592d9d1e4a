package com.pokecobble.phone.manager;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.gui.FarmerAppScreen;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.server.MinecraftServer;

import java.io.*;
import java.lang.reflect.Type;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Server-side manager for farmer app products.
 * Handles product data persistence, synchronization, and management.
 */
public class ServerProductManager {
    private static ServerProductManager instance;
    private static final Gson GSON = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    
    private final Map<String, ServerProductData> products = new ConcurrentHashMap<>();
    private final ReadWriteLock dataLock = new ReentrantReadWriteLock();
    private final Path dataFile;
    private final Path backupFile;
    
    private long lastSaveTime = 0;
    private static final long SAVE_INTERVAL = 30000; // 30 seconds
    
    private ServerProductManager() {
        // Initialize data file paths
        Path configDir = Paths.get("config", "pokecobbleclaim", "phone");
        try {
            Files.createDirectories(configDir);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to create phone config directory", e);
        }
        
        this.dataFile = configDir.resolve("products.json");
        this.backupFile = configDir.resolve("products.json.backup");
        
        loadProducts();
        initializeDefaultProducts();
    }
    
    public static synchronized ServerProductManager getInstance() {
        if (instance == null) {
            instance = new ServerProductManager();
        }
        return instance;
    }
    
    /**
     * Gets all products.
     */
    public Map<String, ServerProductData> getAllProducts() {
        dataLock.readLock().lock();
        try {
            return new HashMap<>(products);
        } finally {
            dataLock.readLock().unlock();
        }
    }
    
    /**
     * Gets a product by ID.
     */
    public ServerProductData getProduct(String productId) {
        dataLock.readLock().lock();
        try {
            return products.get(productId);
        } finally {
            dataLock.readLock().unlock();
        }
    }
    
    /**
     * Updates a product with new data.
     */
    public boolean updateProduct(String productId, String name, int price, String description, 
                               int requiredLevel, Map<String, Object> categoryData, String modifiedBy) {
        dataLock.writeLock().lock();
        try {
            ServerProductData product = products.get(productId);
            if (product == null) {
                Pokecobbleclaim.LOGGER.warn("Attempted to update non-existent product: {}", productId);
                return false;
            }
            
            // Update basic properties
            product.setName(name);
            product.setPrice(price);
            product.setDescription(description);
            product.setRequiredLevel(requiredLevel);
            product.setModifiedBy(modifiedBy);
            
            // Update category-specific data
            if (categoryData != null) {
                product.setCategoryData(new HashMap<>(categoryData));

                // If a selected item ID is provided, update the product's main item ID
                Object selectedItemId = categoryData.get("selected_item_id");
                if (selectedItemId != null && !selectedItemId.toString().isEmpty()) {
                    product.setItemId(selectedItemId.toString());
                    Pokecobbleclaim.LOGGER.info("Updated product {} item ID to: {}", productId, selectedItemId);
                }
            }
            
            // Save changes
            saveProductsAsync();
            
            Pokecobbleclaim.LOGGER.info("Updated product {} by {}", productId, modifiedBy);
            return true;
            
        } finally {
            dataLock.writeLock().unlock();
        }
    }
    
    /**
     * Gets products by category.
     */
    public List<ServerProductData> getProductsByCategory(FarmerAppScreen.ProductType type) {
        dataLock.readLock().lock();
        try {
            return products.values().stream()
                    .filter(product -> product.getType() == type)
                    .sorted(Comparator.comparing(ServerProductData::getName))
                    .toList();
        } finally {
            dataLock.readLock().unlock();
        }
    }
    
    /**
     * Adds a new product.
     */
    public boolean addProduct(ServerProductData product) {
        dataLock.writeLock().lock();
        try {
            if (products.containsKey(product.getId())) {
                Pokecobbleclaim.LOGGER.warn("Attempted to add duplicate product: {}", product.getId());
                return false;
            }
            
            products.put(product.getId(), product);
            saveProductsAsync();
            
            Pokecobbleclaim.LOGGER.info("Added new product: {}", product.getId());
            return true;
            
        } finally {
            dataLock.writeLock().unlock();
        }
    }
    
    /**
     * Removes a product.
     */
    public boolean removeProduct(String productId) {
        dataLock.writeLock().lock();
        try {
            ServerProductData removed = products.remove(productId);
            if (removed != null) {
                saveProductsAsync();
                Pokecobbleclaim.LOGGER.info("Removed product: {}", productId);
                return true;
            }
            return false;
        } finally {
            dataLock.writeLock().unlock();
        }
    }
    
    /**
     * Gets the current data version for synchronization.
     */
    public long getDataVersion() {
        dataLock.readLock().lock();
        try {
            return products.values().stream()
                    .mapToLong(ServerProductData::getDataVersion)
                    .max()
                    .orElse(System.currentTimeMillis());
        } finally {
            dataLock.readLock().unlock();
        }
    }
    
    /**
     * Loads products from storage.
     */
    private void loadProducts() {
        if (!Files.exists(dataFile)) {
            Pokecobbleclaim.LOGGER.info("No product data file found, will create default products");
            return;
        }
        
        try (FileReader reader = new FileReader(dataFile.toFile())) {
            Type type = new TypeToken<Map<String, ServerProductData>>(){}.getType();
            Map<String, ServerProductData> loadedProducts = GSON.fromJson(reader, type);
            
            if (loadedProducts != null) {
                products.clear();
                products.putAll(loadedProducts);
                Pokecobbleclaim.LOGGER.info("Loaded {} products from storage", products.size());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load products, attempting backup restore", e);
            loadFromBackup();
        }
    }
    
    /**
     * Loads products from backup file.
     */
    private void loadFromBackup() {
        if (!Files.exists(backupFile)) {
            Pokecobbleclaim.LOGGER.warn("No backup file found, will use default products");
            return;
        }
        
        try (FileReader reader = new FileReader(backupFile.toFile())) {
            Type type = new TypeToken<Map<String, ServerProductData>>(){}.getType();
            Map<String, ServerProductData> loadedProducts = GSON.fromJson(reader, type);
            
            if (loadedProducts != null) {
                products.clear();
                products.putAll(loadedProducts);
                Pokecobbleclaim.LOGGER.info("Restored {} products from backup", products.size());
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load from backup, using default products", e);
        }
    }
    
    /**
     * Saves products to storage asynchronously.
     */
    private void saveProductsAsync() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastSaveTime < SAVE_INTERVAL) {
            return; // Rate limit saves
        }
        
        lastSaveTime = currentTime;
        
        // Save in background thread
        new Thread(() -> {
            try {
                saveProducts();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to save products", e);
            }
        }, "ProductSaver").start();
    }
    
    /**
     * Saves products to storage synchronously.
     */
    public void saveProducts() throws IOException {
        dataLock.readLock().lock();
        try {
            // Create backup first
            if (Files.exists(dataFile)) {
                Files.copy(dataFile, backupFile, StandardCopyOption.REPLACE_EXISTING);
            }
            
            // Write to temporary file first
            Path tempFile = dataFile.resolveSibling(dataFile.getFileName() + ".tmp");
            try (FileWriter writer = new FileWriter(tempFile.toFile())) {
                GSON.toJson(products, writer);
            }
            
            // Atomic move
            Files.move(tempFile, dataFile, StandardCopyOption.REPLACE_EXISTING);
            
            Pokecobbleclaim.LOGGER.debug("Saved {} products to storage", products.size());
            
        } finally {
            dataLock.readLock().unlock();
        }
    }
    
    /**
     * Initializes default products if none exist.
     */
    private void initializeDefaultProducts() {
        if (!products.isEmpty()) {
            return;
        }
        
        Pokecobbleclaim.LOGGER.info("Initializing default products");
        
        // Seeds
        addDefaultProduct("wheat_seeds", "Wheat Seeds", "High-quality wheat seeds for farming", 
                5, FarmerAppScreen.ProductType.SEED, 1, "wheat_seeds", null);
        addDefaultProduct("carrot_seeds", "Carrot Seeds", "Fresh carrot seeds with high yield", 
                3, FarmerAppScreen.ProductType.SEED, 1, "carrot", null);
        addDefaultProduct("potato_seeds", "Potato Seeds", "Premium potato seeds for large harvests", 
                4, FarmerAppScreen.ProductType.SEED, 1, "potato", null);
        addDefaultProduct("beetroot_seeds", "Beetroot Seeds", "Nutritious beetroot seeds", 
                6, FarmerAppScreen.ProductType.SEED, 2, "beetroot_seeds", null);
        
        // Food
        addDefaultProduct("wheat", "Wheat", "Fresh harvested wheat - sell for coins", 
                8, FarmerAppScreen.ProductType.FOOD, 0, "wheat", null);
        addDefaultProduct("carrots", "Carrots", "Crunchy orange carrots - high demand", 
                6, FarmerAppScreen.ProductType.FOOD, 0, "carrot", null);
        addDefaultProduct("potatoes", "Potatoes", "Versatile potatoes - always in demand", 
                7, FarmerAppScreen.ProductType.FOOD, 0, "potato", null);
        addDefaultProduct("beetroot", "Beetroot", "Healthy beetroot - premium price", 
                10, FarmerAppScreen.ProductType.FOOD, 0, "beetroot", null);
        
        // Tools
        addDefaultProduct("iron_hoe", "Iron Hoe", "Durable iron hoe for efficient farming", 
                25, FarmerAppScreen.ProductType.TOOL, 3, null, "pokecobbleclaim:textures/phone/farmerapp/tools/iron_hoe.png");
        addDefaultProduct("diamond_hoe", "Diamond Hoe", "Premium diamond hoe - fastest farming", 
                50, FarmerAppScreen.ProductType.TOOL, 5, null, "pokecobbleclaim:textures/phone/farmerapp/tools/diamond_hoe.png");
        addDefaultProduct("watering_can", "Watering Can", "Speeds up crop growth significantly", 
                15, FarmerAppScreen.ProductType.TOOL, 2, null, "pokecobbleclaim:textures/phone/farmerapp/tools/watering_can.png");
        
        // Upgrades
        addDefaultProduct("seed_discount", "Seed Price Upgrade", "Reduce seed costs by 15% permanently", 
                100, FarmerAppScreen.ProductType.UPGRADE, 1, null, "pokecobbleclaim:textures/phone/farmerapp/upgrades/seed_discount.png");
        addDefaultProduct("sell_bonus", "Sell Price Upgrade", "Increase crop sell prices by 20%", 
                150, FarmerAppScreen.ProductType.UPGRADE, 2, null, "pokecobbleclaim:textures/phone/farmerapp/upgrades/sell_bonus.png");
        addDefaultProduct("tool_discount", "Tool Price Upgrade", "Reduce tool costs by 25% permanently", 
                200, FarmerAppScreen.ProductType.UPGRADE, 3, null, "pokecobbleclaim:textures/phone/farmerapp/upgrades/tool_discount.png");
        
        // Unlocks
        addDefaultProduct("unlock_sprinkler", "Unlock Sprinkler", "Auto-water crops in 3x3 area", 
                75, FarmerAppScreen.ProductType.UNLOCK, 2, null, "pokecobbleclaim:textures/phone/farmerapp/unlocks/sprinkler.png");
        addDefaultProduct("unlock_harvester", "Unlock Harvester", "Auto-harvest crops in 5x5 area", 
                125, FarmerAppScreen.ProductType.UNLOCK, 4, null, "pokecobbleclaim:textures/phone/farmerapp/unlocks/harvester.png");
        addDefaultProduct("unlock_fertilizer", "Unlock Fertilizer", "Double crop growth speed", 
                90, FarmerAppScreen.ProductType.UNLOCK, 3, null, "pokecobbleclaim:textures/phone/farmerapp/unlocks/fertilizer.png");
        
        try {
            saveProducts();
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save default products", e);
        }
    }
    
    private void addDefaultProduct(String id, String name, String description, int price, 
                                 FarmerAppScreen.ProductType type, int requiredLevel, 
                                 String itemId, String iconPath) {
        ServerProductData product = new ServerProductData(id, name, description, price, type, requiredLevel);
        product.setItemId(itemId);
        product.setIconPath(iconPath);
        product.setModifiedBy("system");
        
        // Set default category-specific data
        switch (type) {
            case SEED:
                product.setGrowthTime(120);
                break;
            case FOOD:
                product.setNutritionValue(5);
                break;
            case TOOL:
                product.setDurability(100);
                break;
            case UPGRADE:
                product.setUpgradeDiscountPercent(15);
                break;
            case UNLOCK:
                product.setUnlockCost(price);
                break;
        }
        
        products.put(id, product);
    }
    
    /**
     * Shuts down the manager and saves all data.
     */
    public void shutdown() {
        try {
            saveProducts();
            Pokecobbleclaim.LOGGER.info("ServerProductManager shutdown complete");
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save products during shutdown", e);
        }
    }
}
