package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.network.ProductUpdatePacket;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.item.ItemStack;
import net.minecraft.text.Text;
import net.minecraft.registry.Registries;

import java.util.HashMap;
import java.util.Map;

/**
 * Modern screen for editing product details in the farmer app.
 * Features MyTownScreen-style glass effects, responsive design, and category-specific editing options.
 */
public class ProductEditScreen extends Screen {
    private final Screen parent;
    private FarmerAppScreen.Product product; // Not final to allow updates from server sync

    // UI Components - category-specific fields
    private TextFieldWidget nameField;
    private TextFieldWidget priceField;
    private TextFieldWidget descriptionField;
    private TextFieldWidget requiredLevelField;

    // Category-specific fields
    private TextFieldWidget discountPercentField; // For UPGRADE products
    private TextFieldWidget unlockCostField; // For UNLOCK products
    private TextFieldWidget growthTimeField; // For SEED products
    private TextFieldWidget nutritionValueField; // For FOOD products
    private TextFieldWidget durabilityField; // For TOOL products

    // Panel dimensions - responsive and matching MyTownScreen style
    private int panelWidth = 600;
    private int panelHeight = 450;

    // Glass Effect Color Palette - matching MyTownScreen styling
    private static final int GLASS_PANEL_BG = 0xD0101010;      // Main panel background
    private static final int GLASS_HEADER_BG = 0x60404040;     // Header glass background
    private static final int GLASS_CONTENT_BG = 0x30000000;    // Content area background
    private static final int GLASS_CARD_BG = 0x40303030;       // Card backgrounds
    private static final int GLASS_CARD_HOVER = 0x60404040;    // Card hover state

    // Glass effect highlights and shadows - matching MyTownScreen
    private static final int GLASS_TOP_HIGHLIGHT = 0x20FFFFFF;    // Top glass highlight
    private static final int GLASS_LEFT_HIGHLIGHT = 0x20FFFFFF;   // Left glass highlight
    private static final int GLASS_BRIGHT_HIGHLIGHT = 0x40FFFFFF; // Brighter highlights
    private static final int GLASS_INNER_HIGHLIGHT = 0x30FFFFFF;  // Inner glass highlights
    private static final int GLASS_SHADOW = 0x40000000;          // Glass shadows
    private static final int GLASS_BOTTOM_SHADOW = 0x20000000;   // Bottom shadows

    // Text colors - matching MyTownScreen
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int TEXT_ACCENT = 0xFF4CAF50;      // Accent color

    // Border and highlight colors - matching MyTownScreen
    private static final int HOVER_OVERLAY = 0x20FFFFFF;    // Hover effect
    private static final int BORDER_COLOR = 0xFF333333;     // Border color
    private static final int FIELD_FOCUS_BORDER = 0xFF4CAF50; // Focus border

    // Compact spacing for better screen utilization
    private static final int SPACING_XS = 2;
    private static final int SPACING_SM = 4;
    private static final int SPACING_MD = 6;
    private static final int SPACING_LG = 8;
    private static final int HEADER_HEIGHT = 20; // Much smaller header
    private static final int BUTTON_AREA_HEIGHT = 30; // Smaller button area

    // Selected item for modification
    private ItemStack selectedProductItem = null;
    
    public ProductEditScreen(Screen parent, FarmerAppScreen.Product product) {
        super(Text.literal("Edit Product: " + product.getName()));
        this.parent = parent;
        this.product = product;
    }

    /**
     * Calculates responsive panel dimensions for maximum screen utilization.
     * Uses much larger screen coverage with compact design.
     */
    private void calculateResponsiveDimensions() {
        // Use minimal margins for maximum screen utilization
        int safeMarginX = Math.max(10, width / 40); // Smaller margins: 2.5% or 10px minimum
        int safeMarginY = Math.max(10, height / 40); // Smaller margins: 2.5% or 10px minimum

        int availableWidth = width - safeMarginX * 2;
        int availableHeight = height - safeMarginY * 2;

        // Much larger preferred dimensions for better screen utilization
        int preferredWidth = Math.min(900, (int)(width * 0.85)); // 85% of screen width or 900px max
        int preferredHeight = Math.min(600, (int)(height * 0.85)); // 85% of screen height or 600px max

        // Compact minimum dimensions
        int minWidth = 400;
        int minHeight = 250;

        // Large maximum dimensions
        int maxWidth = 1000;
        int maxHeight = 700;

        // Calculate optimal dimensions - prioritize using available space
        panelWidth = Math.min(maxWidth, Math.max(minWidth,
                     Math.min(preferredWidth, availableWidth)));

        panelHeight = Math.min(maxHeight, Math.max(minHeight,
                      Math.min(preferredHeight, availableHeight)));

        // Final safety check
        if (panelWidth > availableWidth) panelWidth = availableWidth;
        if (panelHeight > availableHeight) panelHeight = availableHeight;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate responsive panel dimensions that always fit the screen
        calculateResponsiveDimensions();

        // Center panel position - ensuring it stays within screen bounds
        int leftX = (width - panelWidth) / 2;
        int topY = Math.max(10, (height - panelHeight) / 2);

        // Ensure panel doesn't go off screen edges
        leftX = Math.max(10, Math.min(leftX, width - panelWidth - 10));
        topY = Math.max(10, Math.min(topY, height - panelHeight - 10));

        // Compact content area calculations
        int contentX = leftX + SPACING_SM;
        int contentY = topY + HEADER_HEIGHT + SPACING_XS;
        int contentWidth = panelWidth - SPACING_SM * 2;
        int contentHeight = panelHeight - HEADER_HEIGHT - SPACING_XS - BUTTON_AREA_HEIGHT;

        // Side-by-side layout: labels on left, fields on right
        int labelWidth = 120; // Fixed width for labels
        int fieldWidth = Math.min(200, (contentWidth - labelWidth - SPACING_SM * 3) / 2); // Smaller field width
        int fieldHeight = 16; // Smaller field height
        int labelX = contentX + SPACING_SM;
        int fieldX = labelX + labelWidth + SPACING_SM;
        int currentY = contentY + SPACING_XS;

        // Common fields for all product types
        setupCommonFields(fieldX, currentY, fieldWidth, fieldHeight);
        currentY += getCommonFieldsHeight();

        // Category-specific fields
        setupCategorySpecificFields(fieldX, currentY, fieldWidth, fieldHeight);
        currentY += getCategorySpecificFieldsHeight();

        // Item picker button (if applicable) - compact
        if (shouldShowItemPicker()) {
            int itemPickerY = Math.min(currentY + SPACING_SM, topY + panelHeight - 50);

            // Update button text based on whether an item is selected - shorter text
            String buttonText = selectedProductItem != null ?
                "🎯 Change (" + selectedProductItem.getName().getString() + ")" :
                "🎯 Select Item";

            this.addDrawableChild(ButtonWidget.builder(Text.literal(buttonText), button -> {
                openItemPicker();
            }).dimensions(fieldX, itemPickerY, fieldWidth, 18).build()); // Smaller button height
        }

        // Button row at bottom - responsive button sizing
        setupBottomButtons(leftX, topY);
    }

    private void setupCommonFields(int fieldX, int startY, int fieldWidth, int fieldHeight) {
        int currentY = startY;

        // Product name field - side-by-side layout
        nameField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Name"));
        nameField.setText(product.getName());
        nameField.setMaxLength(50);
        nameField.setEditable(true);
        this.addSelectableChild(nameField);
        this.addDrawableChild(nameField);
        currentY += 22;

        // Price field - side-by-side layout
        priceField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Price"));
        priceField.setText(String.valueOf(product.getPrice()));
        priceField.setMaxLength(10);
        priceField.setEditable(true);
        this.addSelectableChild(priceField);
        this.addDrawableChild(priceField);
        currentY += 22;

        // Description field - side-by-side layout
        descriptionField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Description"));
        descriptionField.setText(product.getDescription());
        descriptionField.setMaxLength(100);
        descriptionField.setEditable(true);
        this.addSelectableChild(descriptionField);
        this.addDrawableChild(descriptionField);
        currentY += 22;

        // Required level field - side-by-side layout
        requiredLevelField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Required Level"));
        requiredLevelField.setText(String.valueOf(product.getRequiredLevel()));
        requiredLevelField.setMaxLength(3);
        requiredLevelField.setEditable(true);
        this.addSelectableChild(requiredLevelField);
        this.addDrawableChild(requiredLevelField);
    }

    private void setupCategorySpecificFields(int fieldX, int startY, int fieldWidth, int fieldHeight) {
        int currentY = startY;

        switch (product.getType()) {
            case SEED:
                // Growth time field for seeds - side-by-side layout
                growthTimeField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Growth Time"));
                growthTimeField.setText("120"); // Default growth time in seconds
                growthTimeField.setMaxLength(5);
                growthTimeField.setEditable(true);
                this.addSelectableChild(growthTimeField);
                this.addDrawableChild(growthTimeField);
                break;

            case FOOD:
                // Nutrition value field for food - side-by-side layout
                nutritionValueField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Nutrition Value"));
                nutritionValueField.setText("5"); // Default nutrition value
                nutritionValueField.setMaxLength(3);
                nutritionValueField.setEditable(true);
                this.addSelectableChild(nutritionValueField);
                this.addDrawableChild(nutritionValueField);
                break;

            case TOOL:
                // Durability field for tools - side-by-side layout
                durabilityField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Durability"));
                durabilityField.setText("100"); // Default durability
                durabilityField.setMaxLength(5);
                durabilityField.setEditable(true);
                this.addSelectableChild(durabilityField);
                this.addDrawableChild(durabilityField);
                break;

            case UPGRADE:
                // Discount percent field for upgrades - side-by-side layout
                discountPercentField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Discount Percent"));
                discountPercentField.setText("15"); // Default discount
                discountPercentField.setMaxLength(3);
                discountPercentField.setEditable(true);
                this.addSelectableChild(discountPercentField);
                this.addDrawableChild(discountPercentField);
                break;

            case UNLOCK:
                // Unlock cost field for unlocks - side-by-side layout
                unlockCostField = new TextFieldWidget(this.textRenderer, fieldX, currentY, fieldWidth, fieldHeight, Text.literal("Unlock Cost"));
                unlockCostField.setText(String.valueOf(product.getPrice()));
                unlockCostField.setMaxLength(5);
                unlockCostField.setEditable(true);
                this.addSelectableChild(unlockCostField);
                this.addDrawableChild(unlockCostField);
                break;
        }
    }

    private void setupBottomButtons(int leftX, int topY) {
        int buttonY = topY + panelHeight - BUTTON_AREA_HEIGHT + SPACING_XS;
        int buttonHeight = 18; // Smaller button height
        int buttonSpacing = 6; // Smaller spacing

        // Calculate button width based on available space
        int availableWidth = panelWidth - SPACING_SM * 2;
        int buttonWidth = Math.max(70, (availableWidth - buttonSpacing * 2) / 3); // Min 70px per button
        int totalButtonWidth = buttonWidth * 3 + buttonSpacing * 2;
        int buttonStartX = leftX + (panelWidth - totalButtonWidth) / 2;

        // Save button (compact)
        this.addDrawableChild(ButtonWidget.builder(Text.literal("💾 Save"), button -> {
            saveChanges();
        }).dimensions(buttonStartX, buttonY, buttonWidth, buttonHeight).build());

        // Cancel button (compact)
        this.addDrawableChild(ButtonWidget.builder(Text.literal("❌ Cancel"), button -> {
            this.close();
        }).dimensions(buttonStartX + buttonWidth + buttonSpacing, buttonY, buttonWidth, buttonHeight).build());

        // Reset button (compact)
        this.addDrawableChild(ButtonWidget.builder(Text.literal("🔄 Reset"), button -> {
            resetFields();
        }).dimensions(buttonStartX + (buttonWidth + buttonSpacing) * 2, buttonY, buttonWidth, buttonHeight).build());
    }

    private int getCommonFieldsHeight() {
        return 22 * 4; // 4 common fields with compact 22px spacing each
    }

    private int getCategorySpecificFieldsHeight() {
        return product.getType() != null ? 22 : 0; // 1 category-specific field with compact spacing
    }

    private boolean shouldShowItemPicker() {
        // Show item picker for seeds, food, tools, and unlocks (items with visual representations)
        return product.getType() == FarmerAppScreen.ProductType.SEED ||
               product.getType() == FarmerAppScreen.ProductType.FOOD ||
               product.getType() == FarmerAppScreen.ProductType.TOOL ||
               product.getType() == FarmerAppScreen.ProductType.UNLOCK;
    }
    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);

        // Calculate panel position - ensuring it stays within screen bounds
        int leftX = (width - panelWidth) / 2;
        int topY = Math.max(10, (height - panelHeight) / 2);

        // Ensure panel doesn't go off screen edges
        leftX = Math.max(10, Math.min(leftX, width - panelWidth - 10));
        topY = Math.max(10, Math.min(topY, height - panelHeight - 10));

        // Draw glass effect panel matching MyTownScreen style
        drawGlassPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw glass effect header
        drawGlassHeader(context, leftX, topY, panelWidth, HEADER_HEIGHT);

        // Draw content area with glass effect - compact
        int contentX = leftX + SPACING_SM;
        int contentY = topY + HEADER_HEIGHT + SPACING_XS;
        int contentWidth = panelWidth - SPACING_SM * 2;
        int contentHeight = panelHeight - HEADER_HEIGHT - SPACING_XS - BUTTON_AREA_HEIGHT;

        drawGlassCard(context, contentX, contentY, contentWidth, contentHeight, GLASS_CONTENT_BG, false);

        // Draw field labels and backgrounds with side-by-side layout
        int labelX = contentX + SPACING_SM;
        int fieldY = contentY + SPACING_XS;
        drawSideBySideFieldWithLabel(context, "Product Name:", nameField, labelX, fieldY);
        fieldY += 22;
        drawSideBySideFieldWithLabel(context, "Price (coins):", priceField, labelX, fieldY);
        fieldY += 22;
        drawSideBySideFieldWithLabel(context, "Description:", descriptionField, labelX, fieldY);
        fieldY += 22;
        drawSideBySideFieldWithLabel(context, "Required Level:", requiredLevelField, labelX, fieldY);
        fieldY += 22;

        // Draw category-specific field labels
        drawCategorySpecificFieldLabelsSideBySide(context, labelX, fieldY);

        // Draw product info section with modern styling
        drawProductInfoSection(context, leftX, topY);

        // Draw selected item preview if applicable
        if (selectedProductItem != null && shouldShowItemPicker()) {
            drawSelectedItemPreview(context, leftX, topY);
        }

        super.render(context, mouseX, mouseY, delta);
    }

    private void drawCategorySpecificFieldLabelsSideBySide(DrawContext context, int labelX, int fieldY) {
        switch (product.getType()) {
            case SEED:
                if (growthTimeField != null) {
                    drawSideBySideFieldWithLabel(context, "Growth Time (sec):", growthTimeField, labelX, fieldY);
                }
                break;
            case FOOD:
                if (nutritionValueField != null) {
                    drawSideBySideFieldWithLabel(context, "Nutrition Value:", nutritionValueField, labelX, fieldY);
                }
                break;
            case TOOL:
                if (durabilityField != null) {
                    drawSideBySideFieldWithLabel(context, "Durability:", durabilityField, labelX, fieldY);
                }
                break;
            case UPGRADE:
                if (discountPercentField != null) {
                    drawSideBySideFieldWithLabel(context, "Discount (%):", discountPercentField, labelX, fieldY);
                }
                break;
            case UNLOCK:
                if (unlockCostField != null) {
                    drawSideBySideFieldWithLabel(context, "Unlock Cost:", unlockCostField, labelX, fieldY);
                }
                break;
        }
    }



    private void drawProductInfoSection(DrawContext context, int leftX, int topY) {
        int infoY = topY + panelHeight - 85;
        int infoX = leftX + SPACING_MD;

        // Draw info background with glass effect
        int infoWidth = panelWidth - SPACING_MD * 2;
        int infoHeight = 35;
        drawGlassCard(context, infoX, infoY, infoWidth, infoHeight, GLASS_CARD_BG, false);

        // Product type with icon and color
        String typeIcon = getProductTypeIcon(product.getType());
        String typeInfo = typeIcon + " Type: " + getProductTypeDisplayName(product.getType());
        int typeColor = getProductTypeColor(product.getType());
        context.drawTextWithShadow(this.textRenderer, typeInfo, infoX + SPACING_SM, infoY + SPACING_XS, typeColor);

        // Product ID
        String idInfo = "🆔 ID: " + product.getId();
        context.drawTextWithShadow(this.textRenderer, idInfo, infoX + SPACING_SM, infoY + SPACING_XS + 12, TEXT_MUTED);

        // Category-specific help text
        String helpText = getCategoryHelpText(product.getType());
        if (!helpText.isEmpty()) {
            context.drawTextWithShadow(this.textRenderer, helpText, infoX + SPACING_SM, infoY + SPACING_XS + 24, TEXT_MUTED);
        }
    }

    private int getProductTypeColor(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return 0xFF228B22; // Forest green
            case FOOD: return 0xFFFF8C00; // Dark orange
            case TOOL: return 0xFF4682B4; // Steel blue
            case UPGRADE: return 0xFFFF6B35; // Orange-red for upgrades
            case UNLOCK: return 0xFF35A7FF; // Bright blue for unlocks
            default: return TEXT_SECONDARY;
        }
    }

    private String getCategoryHelpText(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return "💡 Growth time affects how long crops take to mature";
            case FOOD: return "💡 Nutrition value determines hunger restoration";
            case TOOL: return "💡 Durability affects how long the tool lasts";
            case UPGRADE: return "💡 Discount percent reduces prices for players";
            case UNLOCK: return "💡 Unlock cost is paid once to access new features";
            default: return "";
        }
    }

    private String getProductTypeIcon(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return "🌱";
            case FOOD: return "🥕";
            case TOOL: return "🔧";
            case UPGRADE: return "⬆️";
            case UNLOCK: return "🔓";
            default: return "📦";
        }
    }

    private String getProductTypeDisplayName(FarmerAppScreen.ProductType type) {
        switch (type) {
            case SEED: return "Seed";
            case FOOD: return "Food";
            case TOOL: return "Tool";
            case UPGRADE: return "Upgrade";
            case UNLOCK: return "Unlock";
            default: return "Unknown";
        }
    }

    private void drawSelectedItemPreview(DrawContext context, int leftX, int topY) {
        int previewX = leftX + panelWidth - 80; // Right side of panel
        int previewY = topY + HEADER_HEIGHT + SPACING_SM + 10;
        int previewSize = 32;

        // Draw preview background
        drawGlassCard(context, previewX - 4, previewY - 4, previewSize + 8, previewSize + 20, GLASS_CARD_BG, true);

        // Draw the selected item
        try {
            context.drawItem(selectedProductItem, previewX, previewY);
        } catch (Exception e) {
            // Fallback to text if item rendering fails
            context.drawTextWithShadow(this.textRenderer, "?", previewX + 12, previewY + 12, TEXT_SECONDARY);
        }

        // Draw "Preview" label
        String previewLabel = "Preview";
        int labelWidth = this.textRenderer.getWidth(previewLabel);
        int labelX = previewX + (previewSize - labelWidth) / 2;
        context.drawTextWithShadow(this.textRenderer, previewLabel, labelX, previewY + previewSize + 2, TEXT_MUTED);
    }

    /**
     * Draws a glass effect panel matching MyTownScreen styling
     */
    private void drawGlassPanel(DrawContext context, int x, int y, int width, int height) {
        // Main panel background - darker glass effect
        context.fill(x, y, x + width, y + height, GLASS_PANEL_BG);

        // Glass effect borders - matching MyTownScreen style
        context.fill(x, y, x + width, y + 1, GLASS_BRIGHT_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight
        context.fill(x + width - 1, y, x + width, y + height, GLASS_TOP_HIGHLIGHT); // Right highlight
        context.fill(x, y + height - 1, x + width, y + height, GLASS_SHADOW); // Bottom shadow
    }

    /**
     * Draws the glass effect header section matching MyTownScreen styling
     */
    private void drawGlassHeader(DrawContext context, int x, int y, int width, int height) {
        // Header background with glass effect - matching MyTownScreen headers
        context.fill(x, y, x + width, y + height, GLASS_HEADER_BG);

        // Glass effect borders - exactly like MyTownScreen headers
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        // Inner glass effect for depth
        context.fill(x + 1, y + 1, x + width - 1, y + 2, GLASS_TOP_HIGHLIGHT);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);

        // Header title with modern styling
        String title = "✏️ Edit Product: " + product.getName();
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = y + (height - 8) / 2;

        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);
    }

    /**
     * Draws a glass effect card matching the MyTownScreen styling
     */
    private void drawGlassCard(DrawContext context, int x, int y, int width, int height, int bgColor, boolean elevated) {
        // Draw card background
        context.fill(x, y, x + width, y + height, bgColor);

        // Glass effect borders
        context.fill(x, y, x + width, y + 1, GLASS_TOP_HIGHLIGHT); // Top highlight
        context.fill(x, y, x + 1, y + height, GLASS_TOP_HIGHLIGHT); // Left highlight

        if (elevated) {
            // Enhanced glass effect for elevated cards
            context.fill(x + width - 1, y, x + width, y + height, GLASS_BOTTOM_SHADOW); // Right shadow
            context.fill(x, y + height - 1, x + width, y + height, GLASS_BOTTOM_SHADOW); // Bottom shadow
        }
    }



    private void drawSideBySideFieldWithLabel(DrawContext context, String label, TextFieldWidget field, int labelX, int fieldY) {
        // Draw label on the left side, vertically centered with field
        if (field != null) {
            int labelY = fieldY + (field.getHeight() / 2) - (this.textRenderer.fontHeight / 2);
            context.drawTextWithShadow(this.textRenderer, label, labelX, labelY, TEXT_SECONDARY);

            // Draw field background for better visibility
            int fieldX = field.getX();
            int fieldWidth = field.getWidth();
            int fieldHeight = field.getHeight();

            // Modern field background with glass effect
            context.fill(fieldX - 2, fieldY - 2, fieldX + fieldWidth + 2, fieldY + fieldHeight + 2, GLASS_CARD_BG);

            // Modern border with focus state
            int borderColor = field.isFocused() ? FIELD_FOCUS_BORDER : BORDER_COLOR;
            context.drawBorder(fieldX - 2, fieldY - 2, fieldWidth + 4, fieldHeight + 4, borderColor);

            // Subtle inner highlight for modern look
            if (field.isFocused()) {
                context.fill(fieldX - 1, fieldY - 1, fieldX + fieldWidth + 1, fieldY, 0x20FFFFFF);
                context.fill(fieldX - 1, fieldY - 1, fieldX, fieldY + fieldHeight + 1, 0x10FFFFFF);
            }
        }
    }
    
    private void saveChanges() {
        try {
            // Validate common inputs
            String newName = nameField.getText().trim();
            if (newName.isEmpty()) {
                showError("Name cannot be empty!");
                return;
            }

            int newPrice;
            try {
                newPrice = Integer.parseInt(priceField.getText().trim());
                if (newPrice < 0) {
                    showError("Price must be positive!");
                    return;
                }
            } catch (NumberFormatException e) {
                showError("Invalid price format!");
                return;
            }

            String newDescription = descriptionField.getText().trim();
            if (newDescription.isEmpty()) {
                showError("Description cannot be empty!");
                return;
            }

            int newRequiredLevel;
            try {
                newRequiredLevel = Integer.parseInt(requiredLevelField.getText().trim());
                if (newRequiredLevel < 0) {
                    showError("Required level must be non-negative!");
                    return;
                }
            } catch (NumberFormatException e) {
                showError("Invalid required level format!");
                return;
            }

            // Validate and process category-specific fields
            if (!validateCategorySpecificFields()) {
                return; // Error message already shown in validation method
            }

            // Send product update packet to server with category-specific data
            sendProductUpdateToServer(newName, newPrice, newDescription, newRequiredLevel);

            // Log the changes locally
            logProductChanges(newName, newPrice, newDescription, newRequiredLevel);

            // Show success message (but don't close immediately - let server response handle it)
            showSuccessMessage(newName);

            // Don't close immediately - wait for server confirmation
            // The screen will be closed when the server sync is received
            Pokecobbleclaim.LOGGER.info("Product update sent to server, waiting for confirmation...");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error saving product changes", e);
            showError("Failed to save changes!");
        }
    }

    private boolean validateCategorySpecificFields() {
        switch (product.getType()) {
            case SEED:
                if (growthTimeField != null) {
                    try {
                        int growthTime = Integer.parseInt(growthTimeField.getText().trim());
                        if (growthTime <= 0) {
                            showError("Growth time must be positive!");
                            return false;
                        }
                    } catch (NumberFormatException e) {
                        showError("Invalid growth time format!");
                        return false;
                    }
                }
                break;

            case FOOD:
                if (nutritionValueField != null) {
                    try {
                        int nutrition = Integer.parseInt(nutritionValueField.getText().trim());
                        if (nutrition < 0) {
                            showError("Nutrition value must be non-negative!");
                            return false;
                        }
                    } catch (NumberFormatException e) {
                        showError("Invalid nutrition value format!");
                        return false;
                    }
                }
                break;

            case TOOL:
                if (durabilityField != null) {
                    try {
                        int durability = Integer.parseInt(durabilityField.getText().trim());
                        if (durability <= 0) {
                            showError("Durability must be positive!");
                            return false;
                        }
                    } catch (NumberFormatException e) {
                        showError("Invalid durability format!");
                        return false;
                    }
                }
                break;

            case UPGRADE:
                if (discountPercentField != null) {
                    try {
                        int discount = Integer.parseInt(discountPercentField.getText().trim());
                        if (discount < 0 || discount > 100) {
                            showError("Discount percent must be between 0 and 100!");
                            return false;
                        }
                    } catch (NumberFormatException e) {
                        showError("Invalid discount percent format!");
                        return false;
                    }
                }
                break;

            case UNLOCK:
                if (unlockCostField != null) {
                    try {
                        int unlockCost = Integer.parseInt(unlockCostField.getText().trim());
                        if (unlockCost < 0) {
                            showError("Unlock cost must be non-negative!");
                            return false;
                        }
                    } catch (NumberFormatException e) {
                        showError("Invalid unlock cost format!");
                        return false;
                    }
                }
                break;
        }
        return true;
    }

    private void sendProductUpdateToServer(String newName, int newPrice, String newDescription, int newRequiredLevel) {
        // Collect category-specific data
        Map<String, Object> categoryData = new HashMap<>();

        switch (product.getType()) {
            case SEED:
                if (growthTimeField != null) {
                    try {
                        int growthTime = Integer.parseInt(growthTimeField.getText().trim());
                        categoryData.put("growth_time", growthTime);
                    } catch (NumberFormatException e) {
                        categoryData.put("growth_time", 120); // Default
                    }
                }
                break;

            case FOOD:
                if (nutritionValueField != null) {
                    try {
                        int nutrition = Integer.parseInt(nutritionValueField.getText().trim());
                        categoryData.put("nutrition_value", nutrition);
                    } catch (NumberFormatException e) {
                        categoryData.put("nutrition_value", 5); // Default
                    }
                }
                break;

            case TOOL:
                if (durabilityField != null) {
                    try {
                        int durability = Integer.parseInt(durabilityField.getText().trim());
                        categoryData.put("durability", durability);
                    } catch (NumberFormatException e) {
                        categoryData.put("durability", 100); // Default
                    }
                }
                break;

            case UPGRADE:
                if (discountPercentField != null) {
                    try {
                        int discount = Integer.parseInt(discountPercentField.getText().trim());
                        categoryData.put("upgrade_discount_percent", discount);
                    } catch (NumberFormatException e) {
                        categoryData.put("upgrade_discount_percent", 15); // Default
                    }
                }
                break;

            case UNLOCK:
                if (unlockCostField != null) {
                    try {
                        int unlockCost = Integer.parseInt(unlockCostField.getText().trim());
                        categoryData.put("unlock_cost", unlockCost);
                    } catch (NumberFormatException e) {
                        categoryData.put("unlock_cost", newPrice); // Default to price
                    }
                }
                break;
        }

        // Add selected item data if an item was selected
        if (selectedProductItem != null) {
            String itemId = Registries.ITEM.getId(selectedProductItem.getItem()).toString();
            categoryData.put("selected_item_id", itemId);
        }

        // Send enhanced product update with category-specific data
        ProductUpdatePacket.sendToServer(product.getId(), newName, newPrice, newDescription, newRequiredLevel, categoryData);
    }

    private void logProductChanges(String newName, int newPrice, String newDescription, int newRequiredLevel) {
        Pokecobbleclaim.LOGGER.info("Sending product update to server:");
        Pokecobbleclaim.LOGGER.info("  ID: {}", product.getId());
        Pokecobbleclaim.LOGGER.info("  Type: {}", product.getType());
        Pokecobbleclaim.LOGGER.info("  Name: {} -> {}", product.getName(), newName);
        Pokecobbleclaim.LOGGER.info("  Price: {} -> {}", product.getPrice(), newPrice);
        Pokecobbleclaim.LOGGER.info("  Description: {} -> {}", product.getDescription(), newDescription);
        Pokecobbleclaim.LOGGER.info("  Required Level: {} -> {}", product.getRequiredLevel(), newRequiredLevel);

        // Log category-specific changes
        logCategorySpecificChanges();
    }

    private void logCategorySpecificChanges() {
        switch (product.getType()) {
            case SEED:
                if (growthTimeField != null) {
                    Pokecobbleclaim.LOGGER.info("  Growth Time: {}", growthTimeField.getText());
                }
                break;
            case FOOD:
                if (nutritionValueField != null) {
                    Pokecobbleclaim.LOGGER.info("  Nutrition Value: {}", nutritionValueField.getText());
                }
                break;
            case TOOL:
                if (durabilityField != null) {
                    Pokecobbleclaim.LOGGER.info("  Durability: {}", durabilityField.getText());
                }
                break;
            case UPGRADE:
                if (discountPercentField != null) {
                    Pokecobbleclaim.LOGGER.info("  Discount Percent: {}%", discountPercentField.getText());
                }
                break;
            case UNLOCK:
                if (unlockCostField != null) {
                    Pokecobbleclaim.LOGGER.info("  Unlock Cost: {}", unlockCostField.getText());
                }
                break;
        }
    }

    private void showSuccessMessage(String productName) {
        if (this.client != null && this.client.player != null) {
            String typeIcon = getProductTypeIcon(product.getType());
            this.client.player.sendMessage(
                Text.literal("§a✓ " + typeIcon + " Product updated: " + productName),
                true
            );
        }
    }
    
    private void resetFields() {
        // Reset common fields
        if (nameField != null) nameField.setText(product.getName());
        if (priceField != null) priceField.setText(String.valueOf(product.getPrice()));
        if (descriptionField != null) descriptionField.setText(product.getDescription());
        if (requiredLevelField != null) requiredLevelField.setText(String.valueOf(product.getRequiredLevel()));

        // Reset category-specific fields to default values
        switch (product.getType()) {
            case SEED:
                if (growthTimeField != null) growthTimeField.setText("120");
                break;
            case FOOD:
                if (nutritionValueField != null) nutritionValueField.setText("5");
                break;
            case TOOL:
                if (durabilityField != null) durabilityField.setText("100");
                break;
            case UPGRADE:
                if (discountPercentField != null) discountPercentField.setText("15");
                break;
            case UNLOCK:
                if (unlockCostField != null) unlockCostField.setText(String.valueOf(product.getPrice()));
                break;
        }
    }
    
    private void openItemPicker() {
        if (this.client == null) {
            return;
        }

        // Open the item picker screen
        ItemPickerScreen itemPickerScreen = new ItemPickerScreen(this, this::onItemSelected);
        this.client.setScreen(itemPickerScreen);
    }

    private void onItemSelected(net.minecraft.item.ItemStack selectedItem) {
        // Update the product with the selected item
        try {
            // Get the item ID for storage
            String itemId = Registries.ITEM.getId(selectedItem.getItem()).toString();

            // Store the selected item for this product
            selectedProductItem = selectedItem.copy();

            // Update the product's item data immediately for local display
            updateProductItemData(selectedItem, itemId);

            // Refresh the screen to show the new item
            refreshScreen();

            // Show success message
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("§a✓ Item updated: " + selectedItem.getName().getString()),
                    true
                );
            }

            Pokecobbleclaim.LOGGER.info("Updated product {} item to: {} ({})",
                product.getName(), selectedItem.getName().getString(), itemId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to update product item", e);

            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("§cFailed to update item!"),
                    true
                );
            }
        }
    }

    /**
     * Updates the product's item data for immediate local display.
     */
    private void updateProductItemData(ItemStack selectedItem, String itemId) {
        try {
            // Update the product object if it supports item updates
            // This is a temporary local update for immediate visual feedback

            // For now, we'll rely on the selectedProductItem field for display
            // The actual product update will happen when the user saves changes

            Pokecobbleclaim.LOGGER.debug("Updated local product item data for {}: {}",
                product.getName(), itemId);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to update product item data locally", e);
        }
    }

    /**
     * Refreshes the screen to show updated item information.
     */
    private void refreshScreen() {
        try {
            // Clear and reinitialize the screen to show the new item
            this.clearChildren();
            this.init();

            Pokecobbleclaim.LOGGER.debug("Refreshed ProductEditScreen to show updated item");

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refresh ProductEditScreen", e);
        }
    }

    /**
     * Gets the product being edited.
     */
    public FarmerAppScreen.Product getProduct() {
        return product;
    }

    /**
     * Updates the product reference with new data from server sync.
     */
    public void updateProduct(FarmerAppScreen.Product updatedProduct) {
        try {
            this.product = updatedProduct;

            // Update the selected item if the product has an item
            if (updatedProduct.getItemStack() != null && !updatedProduct.getItemStack().isEmpty()) {
                selectedProductItem = updatedProduct.getItemStack().copy();
            }

            // Show success message and close the screen
            if (this.client != null && this.client.player != null) {
                this.client.player.sendMessage(
                    Text.literal("§a✓ Product updated successfully: " + updatedProduct.getName()),
                    true
                );
            }

            // Close the screen after successful server update
            this.close();

            Pokecobbleclaim.LOGGER.info("Product edit completed successfully: {}",
                updatedProduct.getName());

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to update ProductEditScreen with new product data", e);
        }
    }

    private void showError(String message) {
        if (this.client != null && this.client.player != null) {
            this.client.player.sendMessage(
                Text.literal("§c" + message),
                true
            );
        }
    }
    
    @Override
    public void close() {
        this.client.setScreen(parent);
    }
    
    @Override
    public void resize(net.minecraft.client.MinecraftClient client, int width, int height) {
        // Store current field values before resize - common fields
        String currentName = nameField != null ? nameField.getText() : product.getName();
        String currentPrice = priceField != null ? priceField.getText() : String.valueOf(product.getPrice());
        String currentDesc = descriptionField != null ? descriptionField.getText() : product.getDescription();
        String currentLevel = requiredLevelField != null ? requiredLevelField.getText() : String.valueOf(product.getRequiredLevel());

        // Store category-specific field values
        String currentGrowthTime = growthTimeField != null ? growthTimeField.getText() : "120";
        String currentNutrition = nutritionValueField != null ? nutritionValueField.getText() : "5";
        String currentDurability = durabilityField != null ? durabilityField.getText() : "100";
        String currentDiscount = discountPercentField != null ? discountPercentField.getText() : "15";
        String currentUnlockCost = unlockCostField != null ? unlockCostField.getText() : String.valueOf(product.getPrice());

        // Store selected item state
        ItemStack currentSelectedItem = selectedProductItem;

        // Resize the screen - this calls init() which recreates all UI components
        super.resize(client, width, height);

        // CRITICAL: Panel dimensions and positions are recalculated in init()
        // This ensures all click zones work correctly after resize

        // Restore common field values after resize
        if (nameField != null) nameField.setText(currentName);
        if (priceField != null) priceField.setText(currentPrice);
        if (descriptionField != null) descriptionField.setText(currentDesc);
        if (requiredLevelField != null) requiredLevelField.setText(currentLevel);

        // Restore category-specific field values
        if (growthTimeField != null) growthTimeField.setText(currentGrowthTime);
        if (nutritionValueField != null) nutritionValueField.setText(currentNutrition);
        if (durabilityField != null) durabilityField.setText(currentDurability);
        if (discountPercentField != null) discountPercentField.setText(currentDiscount);
        if (unlockCostField != null) unlockCostField.setText(currentUnlockCost);

        // Restore selected item state
        selectedProductItem = currentSelectedItem;
    }

    @Override
    public boolean shouldPause() {
        return false;
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle keyboard shortcuts for better UX
        if (keyCode == 256) { // ESC key
            this.close();
            return true;
        }

        if (keyCode == 257 && (modifiers & 2) != 0) { // Ctrl+Enter
            saveChanges();
            return true;
        }

        if (keyCode == 258) { // Tab key - cycle through fields
            cycleFocus();
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    private void cycleFocus() {
        // Create list of focusable fields in order
        java.util.List<TextFieldWidget> fields = new java.util.ArrayList<>();
        if (nameField != null) fields.add(nameField);
        if (priceField != null) fields.add(priceField);
        if (descriptionField != null) fields.add(descriptionField);
        if (requiredLevelField != null) fields.add(requiredLevelField);

        // Add category-specific fields
        switch (product.getType()) {
            case SEED:
                if (growthTimeField != null) fields.add(growthTimeField);
                break;
            case FOOD:
                if (nutritionValueField != null) fields.add(nutritionValueField);
                break;
            case TOOL:
                if (durabilityField != null) fields.add(durabilityField);
                break;
            case UPGRADE:
                if (discountPercentField != null) fields.add(discountPercentField);
                break;
            case UNLOCK:
                if (unlockCostField != null) fields.add(unlockCostField);
                break;
        }

        // Find currently focused field and move to next
        for (int i = 0; i < fields.size(); i++) {
            if (fields.get(i).isFocused()) {
                fields.get(i).setFocused(false);
                int nextIndex = (i + 1) % fields.size();
                fields.get(nextIndex).setFocused(true);
                return;
            }
        }

        // If no field is focused, focus the first one
        if (!fields.isEmpty()) {
            fields.get(0).setFocused(true);
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle mouse clicks for better field focus management
        boolean result = super.mouseClicked(mouseX, mouseY, button);

        // Add subtle click feedback for better UX
        if (button == 0) { // Left click
            // Check if click is within panel bounds for visual feedback
            int leftX = (width - panelWidth) / 2;
            int topY = Math.max(10, (height - panelHeight) / 2);

            if (mouseX >= leftX && mouseX <= leftX + panelWidth &&
                mouseY >= topY && mouseY <= topY + panelHeight) {
                // Click is within panel - could add click sound or visual effect here
            }
        }

        return result;
    }
}
