package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.manager.ServerProductManager;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

/**
 * Handles product purchase transactions between client and server.
 * Integrates with the economy system for payment processing.
 */
public class ProductPurchasePacket {
    public static final Identifier PRODUCT_PURCHASE_ID = new Identifier("pokecobbleclaim", "product_purchase");
    public static final Identifier PURCHASE_RESULT_ID = new Identifier("pokecobbleclaim", "purchase_result");
    
    /**
     * Sends a product purchase request from client to server.
     * 
     * @param productId The ID of the product to purchase
     * @param quantity The quantity to purchase
     * @param totalPrice The total price for the purchase
     */
    public static void sendPurchaseRequest(String productId, int quantity, long totalPrice) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeString(productId);
        buf.writeInt(quantity);
        buf.writeLong(totalPrice);
        
        ClientPlayNetworking.send(PRODUCT_PURCHASE_ID, buf);
        Pokecobbleclaim.LOGGER.info("Sent purchase request for {} x{} (total: {} coins)", productId, quantity, totalPrice);
    }
    
    /**
     * Registers the server-side purchase handler.
     */
    public static void registerServerHandler() {
        ServerPlayNetworking.registerGlobalReceiver(PRODUCT_PURCHASE_ID, (server, player, handler, buf, responseSender) -> {
            String productId = buf.readString();
            int quantity = buf.readInt();
            long totalPrice = buf.readLong();
            
            server.execute(() -> {
                handleProductPurchase(player, productId, quantity, totalPrice);
            });
        });
    }
    
    /**
     * Registers the client-side result handler.
     */
    public static void registerClientHandler() {
        ClientPlayNetworking.registerGlobalReceiver(PURCHASE_RESULT_ID, (client, handler, buf, responseSender) -> {
            boolean success = buf.readBoolean();
            String message = buf.readString();
            String productName = buf.readString();
            int quantity = buf.readInt();
            
            client.execute(() -> {
                handlePurchaseResult(success, message, productName, quantity);
            });
        });
    }
    
    /**
     * Handles product purchase on the server side.
     */
    private static void handleProductPurchase(ServerPlayerEntity player, String productId, int quantity, long totalPrice) {
        try {
            // Get product data
            ServerProductManager productManager = ServerProductManager.getInstance();
            ServerProductData product = productManager.getProduct(productId);
            
            if (product == null) {
                sendPurchaseResult(player, false, "Product not found!", "", 0);
                return;
            }
            
            // Validate purchase
            if (quantity <= 0) {
                sendPurchaseResult(player, false, "Invalid quantity!", product.getName(), quantity);
                return;
            }
            
            // Calculate expected price
            long expectedPrice = (long) product.getDiscountedPrice() * quantity;
            if (totalPrice != expectedPrice) {
                sendPurchaseResult(player, false, "Price mismatch! Expected: " + expectedPrice, product.getName(), quantity);
                return;
            }
            
            // Check player level requirement
            // TODO: Implement player level checking system
            // For now, assume all players meet requirements
            
            // Process payment through economy system
            boolean paymentSuccess = processPayment(player, totalPrice, 
                "Purchased " + quantity + " " + product.getName());
            
            if (!paymentSuccess) {
                sendPurchaseResult(player, false, "Insufficient funds!", product.getName(), quantity);
                return;
            }
            
            // Give items to player
            boolean itemsGiven = giveItemsToPlayer(player, product, quantity);
            
            if (!itemsGiven) {
                // Refund the payment if items couldn't be given
                refundPayment(player, totalPrice, "Refund for failed purchase: " + product.getName());
                sendPurchaseResult(player, false, "Inventory full! Payment refunded.", product.getName(), quantity);
                return;
            }
            
            // Success!
            sendPurchaseResult(player, true, "Purchase successful!", product.getName(), quantity);
            
            Pokecobbleclaim.LOGGER.info("Player {} purchased {} x{} for {} coins", 
                player.getName().getString(), product.getName(), quantity, totalPrice);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error processing purchase for player " + player.getName().getString(), e);
            sendPurchaseResult(player, false, "Purchase failed due to server error!", "", 0);
        }
    }
    
    /**
     * Processes payment through the economy system.
     */
    private static boolean processPayment(ServerPlayerEntity player, long amount, String description) {
        try {
            // Use the economy system to subtract money from player
            com.pokecobble.economy.api.EconomyAPI economyAPI = com.pokecobble.economy.api.EconomyAPI.getInstance();

            if (!economyAPI.isAvailable()) {
                Pokecobbleclaim.LOGGER.warn("Economy system not available for purchase");
                return false;
            }

            // Check if player has enough money first
            if (!economyAPI.hasEnoughMoney(player.getUuid(), amount)) {
                return false;
            }

            // Subtract money from player's balance
            return economyAPI.subtractMoney(player.getUuid(), amount, description);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to process payment for player " + player.getName().getString(), e);
            return false;
        }
    }

    /**
     * Refunds payment to the player.
     */
    private static void refundPayment(ServerPlayerEntity player, long amount, String description) {
        try {
            com.pokecobble.economy.api.EconomyAPI economyAPI = com.pokecobble.economy.api.EconomyAPI.getInstance();

            if (economyAPI.isAvailable()) {
                economyAPI.addMoney(player.getUuid(), amount, description);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to refund payment to player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Gives purchased items to the player.
     */
    private static boolean giveItemsToPlayer(ServerPlayerEntity player, ServerProductData product, int quantity) {
        try {
            switch (product.getType()) {
                case SEED:
                case FOOD:
                    // Give ItemStack-based items
                    if (product.hasItemStack()) {
                        ItemStack itemStack = product.createItemStack();
                        if (!itemStack.isEmpty()) {
                            itemStack.setCount(quantity);
                            return player.getInventory().insertStack(itemStack);
                        }
                    }
                    break;
                    
                case TOOL:
                    // Give tools (for now, give as items - later could be special tool system)
                    ItemStack toolItem = createToolItem(product);
                    if (!toolItem.isEmpty()) {
                        for (int i = 0; i < quantity; i++) {
                            if (!player.getInventory().insertStack(toolItem.copy())) {
                                return false; // Inventory full
                            }
                        }
                        return true;
                    }
                    break;
                    
                case UPGRADE:
                    // Apply upgrades to player (no physical items)
                    return applyUpgradeToPlayer(player, product, quantity);
                    
                case UNLOCK:
                    // Unlock features for player (no physical items)
                    return unlockFeatureForPlayer(player, product);
            }
            
            return false;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to give items to player " + player.getName().getString(), e);
            return false;
        }
    }
    
    /**
     * Creates a tool item for the player.
     */
    private static ItemStack createToolItem(ServerProductData product) {
        // For now, give basic tools - later this could be enhanced with custom tools
        switch (product.getId()) {
            case "iron_hoe":
                return new ItemStack(Items.IRON_HOE);
            case "diamond_hoe":
                return new ItemStack(Items.DIAMOND_HOE);
            case "watering_can":
                // Custom item - for now give a water bucket as placeholder
                return new ItemStack(Items.WATER_BUCKET);
            default:
                return ItemStack.EMPTY;
        }
    }
    
    /**
     * Applies an upgrade to the player.
     */
    private static boolean applyUpgradeToPlayer(ServerPlayerEntity player, ServerProductData product, int quantity) {
        // TODO: Implement player upgrade system
        // For now, just log the upgrade application
        Pokecobbleclaim.LOGGER.info("Applied upgrade {} x{} to player {}", 
            product.getName(), quantity, player.getName().getString());
        
        // Send notification to player
        player.sendMessage(Text.literal("§a✓ Upgrade applied: " + product.getName()), false);
        
        return true;
    }
    
    /**
     * Unlocks a feature for the player.
     */
    private static boolean unlockFeatureForPlayer(ServerPlayerEntity player, ServerProductData product) {
        // TODO: Implement player unlock system
        // For now, just log the unlock
        Pokecobbleclaim.LOGGER.info("Unlocked feature {} for player {}", 
            product.getName(), player.getName().getString());
        
        // Send notification to player
        player.sendMessage(Text.literal("§a🔓 Feature unlocked: " + product.getName()), false);
        
        return true;
    }
    
    /**
     * Sends purchase result to the client.
     */
    private static void sendPurchaseResult(ServerPlayerEntity player, boolean success, String message, 
                                         String productName, int quantity) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeBoolean(success);
            buf.writeString(message);
            buf.writeString(productName);
            buf.writeInt(quantity);
            
            ServerPlayNetworking.send(player, PURCHASE_RESULT_ID, buf);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send purchase result to player " + player.getName().getString(), e);
        }
    }
    
    /**
     * Handles purchase result on the client side.
     */
    private static void handlePurchaseResult(boolean success, String message, String productName, int quantity) {
        try {
            // Show result to player
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client.player != null) {
                String prefix = success ? "§a✓ " : "§c✗ ";
                String quantityText = quantity > 1 ? " x" + quantity : "";
                String fullMessage = prefix + message + 
                    (!productName.isEmpty() ? " (" + productName + quantityText + ")" : "");
                
                client.player.sendMessage(Text.literal(fullMessage), true);
            }
            
            Pokecobbleclaim.LOGGER.info("Purchase result: {} - {}", success ? "SUCCESS" : "FAILED", message);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle purchase result", e);
        }
    }
}
