package com.pokecobble.phone.network;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.client.ClientProductManager;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.manager.ServerProductManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.Identifier;

import java.util.Map;

/**
 * Manages all product-related network communication between client and server.
 * Handles product updates, synchronization, and full sync requests.
 */
public class ProductNetworkManager {
    public static final Identifier PRODUCT_SYNC_REQUEST = new Identifier("pokecobbleclaim", "product_sync_request");
    public static final Identifier PRODUCT_FULL_SYNC = new Identifier("pokecobbleclaim", "product_full_sync");
    
    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register product update handler
        ProductUpdatePacket.registerServerHandler();
        
        // Register product sync request handler
        ServerPlayNetworking.registerGlobalReceiver(PRODUCT_SYNC_REQUEST, (server, player, handler, buf, responseSender) -> {
            long clientVersion = buf.readLong();
            
            server.execute(() -> {
                handleProductSyncRequest(player, clientVersion);
            });
        });
        
        Pokecobbleclaim.LOGGER.info("Registered server-side product network handlers");
    }
    
    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register product sync handler
        ProductUpdatePacket.registerClientSyncHandler();
        
        // Register full sync handler
        ClientPlayNetworking.registerGlobalReceiver(PRODUCT_FULL_SYNC, (client, handler, buf, responseSender) -> {
            int productCount = buf.readInt();
            
            client.execute(() -> {
                handleFullProductSync(productCount, buf);
            });
        });
        
        Pokecobbleclaim.LOGGER.info("Registered client-side product network handlers");
    }
    
    /**
     * Handles product sync requests from clients.
     */
    private static void handleProductSyncRequest(ServerPlayerEntity player, long clientVersion) {
        try {
            ServerProductManager productManager = ServerProductManager.getInstance();
            Map<String, ServerProductData> allProducts = productManager.getAllProducts();
            
            // Check if client needs a full sync
            long serverVersion = productManager.getDataVersion();
            boolean needsFullSync = clientVersion < serverVersion || allProducts.isEmpty();
            
            if (needsFullSync) {
                sendFullProductSync(player, allProducts);
            } else {
                // Client is up to date
                Pokecobbleclaim.LOGGER.debug("Client {} is up to date (version: {})", 
                    player.getName().getString(), clientVersion);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle product sync request from " + 
                player.getName().getString(), e);
        }
    }
    
    /**
     * Sends full product synchronization to a player.
     */
    public static void sendFullProductSync(ServerPlayerEntity player, Map<String, ServerProductData> products) {
        try {
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeInt(products.size());
            
            for (ServerProductData product : products.values()) {
                // Write product data
                buf.writeString(product.getId());
                buf.writeString(product.getName());
                buf.writeInt(product.getPrice());
                buf.writeString(product.getDescription());
                buf.writeInt(product.getRequiredLevel());
                buf.writeString(product.getType().name());
                buf.writeString(product.getIconPath() != null ? product.getIconPath() : "");
                buf.writeString(product.getItemId() != null ? product.getItemId() : "");
                buf.writeBoolean(product.isOnSale());
                buf.writeInt(product.getDiscountPercent());
                
                // Serialize category data
                String categoryDataJson = new com.google.gson.Gson().toJson(product.getCategoryData());
                buf.writeString(categoryDataJson);
                
                // Data version
                buf.writeLong(product.getDataVersion());
            }
            
            ServerPlayNetworking.send(player, PRODUCT_FULL_SYNC, buf);
            
            Pokecobbleclaim.LOGGER.info("Sent full product sync to {} ({} products)", 
                player.getName().getString(), products.size());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send full product sync to " + 
                player.getName().getString(), e);
        }
    }
    
    /**
     * Sends full product synchronization to all connected players.
     */
    public static void sendFullProductSyncToAll(net.minecraft.server.MinecraftServer server) {
        try {
            ServerProductManager productManager = ServerProductManager.getInstance();
            Map<String, ServerProductData> allProducts = productManager.getAllProducts();
            
            int playerCount = 0;
            for (ServerPlayerEntity player : server.getPlayerManager().getPlayerList()) {
                sendFullProductSync(player, allProducts);
                playerCount++;
            }
            
            Pokecobbleclaim.LOGGER.info("Sent full product sync to {} players", playerCount);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to send full product sync to all players", e);
        }
    }
    
    /**
     * Handles full product synchronization on the client side.
     */
    @Environment(EnvType.CLIENT)
    private static void handleFullProductSync(int productCount, PacketByteBuf buf) {
        try {
            ClientProductManager clientManager = ClientProductManager.getInstance();
            
            // Clear existing cache
            clientManager.clearCache();
            
            // Read all products
            for (int i = 0; i < productCount; i++) {
                String productId = buf.readString();
                String name = buf.readString();
                int price = buf.readInt();
                String description = buf.readString();
                int requiredLevel = buf.readInt();
                String typeName = buf.readString();
                String iconPath = buf.readString();
                String itemId = buf.readString();
                boolean onSale = buf.readBoolean();
                int discountPercent = buf.readInt();
                String categoryDataJson = buf.readString();
                long dataVersion = buf.readLong();
                
                // Parse category data
                java.util.Map<String, Object> categoryData = new java.util.HashMap<>();
                try {
                    com.google.gson.reflect.TypeToken<java.util.Map<String, Object>> typeToken = 
                        new com.google.gson.reflect.TypeToken<java.util.Map<String, Object>>(){};
                    categoryData = new com.google.gson.Gson().fromJson(categoryDataJson, typeToken.getType());
                    if (categoryData == null) {
                        categoryData = new java.util.HashMap<>();
                    }
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.warn("Failed to parse category data for product {}: {}", 
                        productId, e.getMessage());
                    categoryData = new java.util.HashMap<>();
                }
                
                // Update client cache
                clientManager.updateProduct(productId, name, price, description, requiredLevel, 
                                          typeName, iconPath, itemId, onSale, discountPercent, 
                                          categoryData, dataVersion);
            }
            
            Pokecobbleclaim.LOGGER.info("Received full product sync: {} products", productCount);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle full product sync", e);
        }
    }
    
    /**
     * Requests a product sync from the server (client-side).
     */
    @Environment(EnvType.CLIENT)
    public static void requestProductSync() {
        try {
            ClientProductManager clientManager = ClientProductManager.getInstance();
            long currentVersion = clientManager.getLastSyncVersion();
            
            PacketByteBuf buf = PacketByteBufs.create();
            buf.writeLong(currentVersion);
            
            ClientPlayNetworking.send(PRODUCT_SYNC_REQUEST, buf);
            
            Pokecobbleclaim.LOGGER.info("Requested product sync from server (current version: {})", currentVersion);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to request product sync", e);
        }
    }
    
    /**
     * Initializes the product network system.
     */
    public static void initialize() {
        // This method can be called from both client and server
        Pokecobbleclaim.LOGGER.info("Initializing product network system");
    }
}
