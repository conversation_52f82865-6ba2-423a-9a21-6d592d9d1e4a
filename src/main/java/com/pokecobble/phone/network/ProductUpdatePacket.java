package com.pokecobble.phone.network;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.data.ServerProductData;
import com.pokecobble.phone.gui.FarmerAppScreen;
import com.pokecobble.phone.manager.ServerProductManager;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

/**
 * Enhanced product update packet system with category-specific data support.
 * Handles product updates and synchronization between client and server.
 */
public class ProductUpdatePacket {
    public static final Identifier PRODUCT_UPDATE_ID = new Identifier("pokecobbleclaim", "product_update");
    public static final Identifier PRODUCT_SYNC_ID = new Identifier("pokecobbleclaim", "product_sync");

    private static final Gson GSON = new Gson();

    /**
     * Sends a product update packet from client to server with category-specific data.
     *
     * @param productId The ID of the product to update
     * @param newName The new name for the product
     * @param newPrice The new price for the product
     * @param newDescription The new description for the product
     * @param newRequiredLevel The new required level for the product
     * @param categoryData Category-specific data map
     */
    public static void sendToServer(String productId, String newName, int newPrice, String newDescription,
                                  int newRequiredLevel, Map<String, Object> categoryData) {
        PacketByteBuf buf = PacketByteBufs.create();
        buf.writeString(productId);
        buf.writeString(newName);
        buf.writeInt(newPrice);
        buf.writeString(newDescription);
        buf.writeInt(newRequiredLevel);

        // Serialize category data as JSON
        String categoryDataJson = categoryData != null ? GSON.toJson(categoryData) : "{}";
        buf.writeString(categoryDataJson);

        ClientPlayNetworking.send(PRODUCT_UPDATE_ID, buf);
        Pokecobbleclaim.LOGGER.info("Sent enhanced product update packet for: {}", productId);
    }

    /**
     * Legacy method for backward compatibility.
     */
    public static void sendToServer(String productId, String newName, int newPrice, String newDescription, int newRequiredLevel) {
        sendToServer(productId, newName, newPrice, newDescription, newRequiredLevel, new HashMap<>());
    }
    
    /**
     * Registers the enhanced server-side packet handler.
     */
    public static void registerServerHandler() {
        ServerPlayNetworking.registerGlobalReceiver(PRODUCT_UPDATE_ID, (server, player, handler, buf, responseSender) -> {
            // Read packet data
            String productId = buf.readString();
            String newName = buf.readString();
            int newPrice = buf.readInt();
            String newDescription = buf.readString();
            int newRequiredLevel = buf.readInt();
            String categoryDataJson = buf.readString();

            // Process on server thread
            server.execute(() -> {
                handleProductUpdate(player, productId, newName, newPrice, newDescription, newRequiredLevel, categoryDataJson);
            });
        });
    }

    /**
     * Handles the enhanced product update on the server side.
     */
    private static void handleProductUpdate(ServerPlayerEntity player, String productId, String newName,
                                          int newPrice, String newDescription, int newRequiredLevel, String categoryDataJson) {
        try {
            // Check if player has permission to modify products
            if (!player.hasPermissionLevel(2)) {
                player.sendMessage(Text.literal("§cYou don't have permission to modify products!"), false);
                return;
            }

            // Parse category data
            Map<String, Object> categoryData = new HashMap<>();
            try {
                Type type = new com.google.gson.reflect.TypeToken<Map<String, Object>>(){}.getType();
                categoryData = GSON.fromJson(categoryDataJson, type);
                if (categoryData == null) {
                    categoryData = new HashMap<>();
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to parse category data for product {}: {}", productId, e.getMessage());
                categoryData = new HashMap<>();
            }

            // Apply the product changes to the server-side data
            ServerProductManager productManager = ServerProductManager.getInstance();
            boolean success = productManager.updateProduct(productId, newName, newPrice, newDescription,
                                                         newRequiredLevel, categoryData, player.getName().getString());

            if (success) {
                // Send confirmation to the player
                player.sendMessage(Text.literal("§aProduct updated successfully: " + newName), false);

                // Broadcast the changes to all players
                broadcastProductUpdate(player.getServer(), productId);

                Pokecobbleclaim.LOGGER.info("Product {} updated by {}: name={}, price={}, level={}",
                    productId, player.getName().getString(), newName, newPrice, newRequiredLevel);
            } else {
                player.sendMessage(Text.literal("§cFailed to update product: Product not found!"), false);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling product update", e);
            player.sendMessage(Text.literal("§cFailed to update product: " + e.getMessage()), false);
        }
    }
    
    /**
     * Broadcasts the product update to all players with full synchronization.
     */
    private static void broadcastProductUpdate(net.minecraft.server.MinecraftServer server, String productId) {
        try {
            ServerProductManager productManager = ServerProductManager.getInstance();
            ServerProductData product = productManager.getProduct(productId);

            if (product == null) {
                Pokecobbleclaim.LOGGER.warn("Cannot broadcast update for non-existent product: {}", productId);
                return;
            }

            // Create sync packet with full product data
            PacketByteBuf broadcastBuf = PacketByteBufs.create();
            broadcastBuf.writeString(product.getId());
            broadcastBuf.writeString(product.getName());
            broadcastBuf.writeInt(product.getPrice());
            broadcastBuf.writeString(product.getDescription());
            broadcastBuf.writeInt(product.getRequiredLevel());
            broadcastBuf.writeString(product.getType().name());
            broadcastBuf.writeString(product.getIconPath() != null ? product.getIconPath() : "");
            broadcastBuf.writeString(product.getItemId() != null ? product.getItemId() : "");
            broadcastBuf.writeBoolean(product.isOnSale());
            broadcastBuf.writeInt(product.getDiscountPercent());

            // Serialize category data
            String categoryDataJson = GSON.toJson(product.getCategoryData());
            broadcastBuf.writeString(categoryDataJson);

            // Data version for synchronization
            broadcastBuf.writeLong(product.getDataVersion());

            // Send to all connected players
            int playerCount = 0;
            for (ServerPlayerEntity serverPlayer : server.getPlayerManager().getPlayerList()) {
                ServerPlayNetworking.send(serverPlayer, PRODUCT_SYNC_ID, broadcastBuf);
                playerCount++;
            }

            Pokecobbleclaim.LOGGER.info("Broadcasted product update for {} to {} players", productId, playerCount);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to broadcast product update for " + productId, e);
        }
    }

    /**
     * Registers the client-side sync packet handler.
     */
    public static void registerClientSyncHandler() {
        ClientPlayNetworking.registerGlobalReceiver(PRODUCT_SYNC_ID, (client, handler, buf, responseSender) -> {
            // Read product data from server
            String productId = buf.readString();
            String name = buf.readString();
            int price = buf.readInt();
            String description = buf.readString();
            int requiredLevel = buf.readInt();
            String typeName = buf.readString();
            String iconPath = buf.readString();
            String itemId = buf.readString();
            boolean onSale = buf.readBoolean();
            int discountPercent = buf.readInt();
            String categoryDataJson = buf.readString();
            long dataVersion = buf.readLong();

            // Process on client thread
            client.execute(() -> {
                handleProductSync(productId, name, price, description, requiredLevel, typeName,
                                iconPath, itemId, onSale, discountPercent, categoryDataJson, dataVersion);
            });
        });
    }

    /**
     * Handles product synchronization on the client side.
     */
    private static void handleProductSync(String productId, String name, int price, String description,
                                        int requiredLevel, String typeName, String iconPath, String itemId,
                                        boolean onSale, int discountPercent, String categoryDataJson, long dataVersion) {
        try {
            // Parse category data
            Map<String, Object> categoryData = new HashMap<>();
            try {
                com.google.gson.reflect.TypeToken<Map<String, Object>> typeToken =
                    new com.google.gson.reflect.TypeToken<Map<String, Object>>(){};
                categoryData = GSON.fromJson(categoryDataJson, typeToken.getType());
                if (categoryData == null) {
                    categoryData = new HashMap<>();
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.warn("Failed to parse category data for product {}: {}", productId, e.getMessage());
                categoryData = new HashMap<>();
            }

            // Update client-side product cache
            com.pokecobble.phone.client.ClientProductManager.getInstance().updateProduct(
                productId, name, price, description, requiredLevel, typeName, iconPath, itemId,
                onSale, discountPercent, categoryData, dataVersion
            );

            Pokecobbleclaim.LOGGER.info("Synchronized product {}: {} (version: {})", productId, name, dataVersion);

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to handle product sync for " + productId, e);
        }
    }
}
